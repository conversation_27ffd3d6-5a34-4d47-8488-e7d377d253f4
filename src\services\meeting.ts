import request from '@/utils/request';

/** 会议纪要生成 - 获取配置信息 */
export const getConfigApi = '/ai/meet/getConfig';

/** 会议纪要转写 - 提交任务 */
export const meetGenerateApi = '/ai/meet/generate';

/** 会议纪要生成 - 获取历史记录 */
export const getHistoryApi = '/ai/meet/getHistory';

/** 会议纪要转写 - 获取任务信息 */
export const meetTaskInfoByIdApi = '/ai/meet/getTaskInfoById';

/** 会议纪要转写 - 重新转写 */
export const meetRewriteApi = '/ai/meet/rewrite';

/** 会议纪要转写 - 智能总结下载 */
export const meetDownLoadApi = '/ai/meet/downLoad';

/** 会议纪要转写 - 修改发言内容 */
export const updateSpeechContentApi = '/ai/meet/updateSpeechContent';

/** 会议纪要转写 - 任务删除 */
export const deleteTaskApi = '/ai/meet/deleteById';

/** 会议纪要转写 - 任务回调 */
export const meetTaskCallbackApi = '/ai/meet/callBack';

/** 会议纪要转写 - 任务终止 */
export const meetTaskStopApi = '/ai/meet/taskStop';

/** 会议纪要转写 - 智能总结 */
export const meetSummaryApi = '/ai/meet/aiSummary';

export type ConfigSelectType = {
  code: string | boolean;
  name: string;
  isSelect: boolean;
};

export type ConfigType = {
  languageCodes: ConfigSelectType[];
  speakerCodes: ConfigSelectType[];
};

export type HistoryItemType = {
  id: number;
  fileName: string;
  fileSize: string;
  fileTypeName: string;
  duration: string;
  statusName: string;
  createTime: string;
};

export type HistoryType = {
  list: HistoryItemType[];
  total: number;
};

export interface MeetGenerateParams {
  url: string;
  fileName: string;
  fileSize: string;
  fileType: number; // 文件类型：0=音频 1=视频
  language: string;
  speakers: boolean;
}

// 会议摘要内容
export interface SpokesSummaryType {
  spkName: string; // 发言人名称
  spkSummary: string; // 总结内容
}

// 管理后台 - 会议纪要总结
export interface AiSummaryType {
  keyWords: string[]; // 关键词列表
  meetSummary: string; // 会议概要
  issues: string; // 关键议题
  spokesSummary: SpokesSummaryType[]; // 会议摘要
}

// 会议详情类
export interface DialogueRecordType {
  id: number; // 会议详情ID
  personName: string; //	发言人名称
  startTimeStr: string; // 	发言开始时间（格式化）
  startTime: number; // 	发言开始时间（毫秒）
  endTime: number; // 	发言结束时间（毫秒）
  speechContent: string; // 	发言内容
}

export interface MeetTaskInfo {
  meetTaskId: number; //	会议纪要任务ID
  duration: number; // 总时长
  audioUrl: string; //音频链接
  aiSummary: AiSummaryType; // 管理后台 - 会议纪要总结
  details: DialogueRecordType[]; // 会议详情类
}

// 修改发言内容参数
export interface UpdateSpeechContentParams {
  id: string | number | null; // 	会议详情ID
  speechContent: string; // 发言内容
}

/**
 * 会议纪要撰写 - 获取配置信息
 */
export const getConfig = () =>
  request<ConfigType>(getConfigApi, {
    method: 'GET',
  });

/**
 * 会议纪要撰写 - 提交任务
 */
export const fetchMeetGenerate = (data: MeetGenerateParams) =>
  request(meetGenerateApi, {
    method: 'POST',
    data,
  });

/**
 * 会议纪要撰写 - 获取历史记录
 */
export function getHistory(data: { pageNo: number; pageSize: number; fileName: string }) {
  return request<HistoryType>(getHistoryApi, {
    method: 'POST',
    data,
  });
}

/**
 * 会议纪要撰写 - 获取任务信息
 */
export const fetchMeetTaskInfoById = (params: { id: string }) =>
  request<MeetTaskInfo>(meetTaskInfoByIdApi, {
    method: 'GET',
    params,
  });

/**
 * 会议纪要撰写 - 重新转写
 */
export const fetchMeetRewrite = (params: { id: string }) =>
  request(meetRewriteApi, {
    method: 'GET',
    params,
  });

/**
 * 会议纪要撰写 - 智能总结下载
 */
export const fetchMeetDownLoad = (params: { id: string }) =>
  request<{ url: string; fileName: string }>(meetDownLoadApi, {
    method: 'GET',
    params,
  });

/**
 * 会议纪要撰写 - 修改发言内容
 */
export const updateSpeechContent = (data: UpdateSpeechContentParams) =>
  request(updateSpeechContentApi, {
    method: 'POST',
    data,
  });

/**
 * 会议纪要撰写 - 任务删除
 */
export const deleteTask = (params: { id: string }) =>
  request(deleteTaskApi, {
    method: 'GET',
    params,
  });

/**
 * 会议纪要撰写 - 任务回调
 */
export const meetTaskCallback = (data: any) =>
  request(meetTaskCallbackApi, {
    method: 'POST',
    data,
  });

/**
 * 会议纪要撰写 - 任务终止
 */
export const meetTaskStop = (params: { id: string }) =>
  request(meetTaskStopApi, {
    method: 'GET',
    params,
  });

/**
 * 会议纪要撰写 - 智能总结
 */
export const meetSummary = (params: { id: string }) =>
  request<AiSummaryType>(meetSummaryApi, {
    method: 'GET',
    params,
  });
