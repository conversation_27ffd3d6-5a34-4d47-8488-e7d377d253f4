import {
  AudioBackwardDisabledIcon,
  AudioBackwardIcon,
  AudioForwardDisabledIcon,
  AudioForwardIcon,
  AudioMutedIcon,
  AudioNotMutedIcon,
  AudioPauseIcon,
  AudioPlayIcon,
  BackTopIcon,
  EditIcon,
  MeetingAvatarIcon,
} from '@/assets/svg';
import { DialogueRecordType, updateSpeechContent } from '@/services/meeting';
import { secondsToTimestamp } from '@/utils';
import { Button, Select, Spin, Tag, TextArea, Toast, Tooltip, Typography } from '@douyinfe/semi-ui';
import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import { useSelector } from 'umi';
import styles from '../index.less';

export interface AudioTranscriptPlayerProps {
  audioSrc: string;
  transcript: DialogueRecordType[];
  autoPlay?: boolean;
  duration?: number;
  loading?: boolean;
}

const PLAYBACK_RATES = [0.5, 1, 1.5, 2] as const;
const PLAYBACK_RATE_OPTIONS = PLAYBACK_RATES.map((rate) => ({
  label: `${rate}x`,
  value: rate,
}));
type PlaybackRate = (typeof PLAYBACK_RATES)[number];

const initialState = {
  isReady: false,
  playing: false,
  muted: false,
  volume: 1,
  playbackRate: 1 as PlaybackRate,
  played: 0,
  loaded: 0,
  duration: 0,
  seeking: false,
  loadedSeconds: 0,
  playedSeconds: 0,
};

type PlayerState = typeof initialState;

const AudioTranscriptPlayer: React.FC<AudioTranscriptPlayerProps> = ({
  audioSrc,
  transcript,
  autoPlay,
  duration,
  loading,
}) => {
  const { Paragraph } = Typography;
  // 播放器和DOM引用
  const playerRef = useRef<any>(null);
  const listContainerRef = useRef<HTMLDivElement | null>(null);
  const segmentRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  const pageMode: string = useSelector(
    (state: { pageLayout: { mode: '' } }) => state.pageLayout.mode,
  );

  // 状态管理
  const [state, setState] = useState<PlayerState>({
    ...initialState,
    playing: !!autoPlay,
  });
  const [transcriptData, setTranscriptData] = useState<DialogueRecordType[]>([]);
  const [activeIndex, setActiveIndex] = useState<number>(-1);
  const [editingId, setEditingId] = useState<string | number | null>(null);
  const [isSelectRate, setIsSelectRate] = useState<Boolean>(false);
  const [editingText, setEditingText] = useState('');

  const manualClickRef = useRef(false);
  const pendingSeekRef = useRef<number | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollTimeRef = useRef<number>(0);
  const lastActiveIndexRef = useRef<number>(-1);

  // 虚拟滚动相关状态
  const ESTIMATED_ITEM_HEIGHT = 120;
  const OVERSCAN_COUNT = 6;
  const [scrollTop, setScrollTop] = useState<number>(0);
  const [viewportHeight, setViewportHeight] = useState<number>(0);
  const itemHeightsRef = useRef<Map<string, number>>(new Map());
  const [heightsVersion, setHeightsVersion] = useState<number>(0);
  const isUserScrollingRef = useRef<boolean>(false);
  const scrollIdleTimeoutRef = useRef<number | null>(null);

  // 获取有效时长
  const getEffectiveDuration = useCallback(() => {
    const mediaDuration = playerRef.current?.duration;
    if (typeof mediaDuration === 'number' && isFinite(mediaDuration) && mediaDuration > 0) {
      return mediaDuration;
    }
    return duration || state.duration || 0;
  }, [state.duration, duration]);

  // 初始化转录数据
  useEffect(() => {
    setTranscriptData(transcript);
    if (typeof duration === 'number' && isFinite(duration) && duration > 0) {
      setState((prev) => ({ ...prev, duration }));
    }
  }, [transcript, duration]);

  // 按时间排序的转录片段
  const orderedTranscript = useMemo(() => {
    return [...transcriptData].sort((a, b) => a.startTime - b.startTime);
  }, [transcriptData]);

  // 计算总高度和偏移量
  const { totalHeight, offsets } = useMemo(() => {
    const n = orderedTranscript.length;
    const heights = new Array(n).fill(ESTIMATED_ITEM_HEIGHT);
    const offsets = new Array(n);

    let total = 0;
    for (let i = 0; i < n; i++) {
      const id = String(orderedTranscript[i].id);
      const h = itemHeightsRef.current.get(id) || ESTIMATED_ITEM_HEIGHT;
      heights[i] = h;
      offsets[i] = total;
      total += h;
    }

    return { totalHeight: total, offsets };
  }, [orderedTranscript, heightsVersion]);

  // 视窗变化和滚动监听
  useEffect(() => {
    const container = listContainerRef.current;
    if (!container) return;

    setViewportHeight(container.clientHeight);

    const onScroll = () => {
      setScrollTop(container.scrollTop);
      isUserScrollingRef.current = true;

      if (scrollIdleTimeoutRef.current) {
        clearTimeout(scrollIdleTimeoutRef.current);
      }

      scrollIdleTimeoutRef.current = window.setTimeout(() => {
        isUserScrollingRef.current = false;
      }, 150);
    };

    container.addEventListener('scroll', onScroll, { passive: true });

    // ResizeObserver 监听容器高度变化
    const ro = new ResizeObserver(() => {
      setViewportHeight(container.clientHeight);
    });

    ro.observe(container);

    return () => {
      container.removeEventListener('scroll', onScroll);
      ro.disconnect();
      if (scrollIdleTimeoutRef.current) clearTimeout(scrollIdleTimeoutRef.current);
    };
  }, []);

  // 计算可见区域
  const { startIndex, endIndex, topSpacerHeight, bottomSpacerHeight } = useMemo(() => {
    if (viewportHeight === 0 || orderedTranscript.length === 0) {
      return {
        startIndex: 0,
        endIndex: Math.min(20, orderedTranscript.length - 1),
        topSpacerHeight: 0,
        bottomSpacerHeight: 0,
      };
    }

    // 找到开始索引
    let startIdx = 0;
    for (; startIdx < orderedTranscript.length; startIdx++) {
      if ((offsets[startIdx] || 0) + ESTIMATED_ITEM_HEIGHT > scrollTop) break;
    }

    startIdx = Math.max(0, startIdx - OVERSCAN_COUNT);

    // 找到结束索引
    const viewBottom = scrollTop + viewportHeight;
    let endIdx = startIdx;

    for (; endIdx < orderedTranscript.length; endIdx++) {
      if ((offsets[endIdx] || 0) > viewBottom) break;
    }

    endIdx = Math.min(orderedTranscript.length - 1, endIdx + OVERSCAN_COUNT);

    // 计算底部占位高度（更精确的计算）
    const bottomSpacer = Math.max(
      0,
      totalHeight -
        (offsets[endIdx] || 0) -
        (itemHeightsRef.current.get(String(orderedTranscript[endIdx]?.id)) ||
          ESTIMATED_ITEM_HEIGHT),
    );

    return {
      startIndex: startIdx,
      endIndex: endIdx,
      topSpacerHeight: offsets[startIdx] || 0,
      bottomSpacerHeight: bottomSpacer,
    };
  }, [scrollTop, viewportHeight, orderedTranscript, offsets, totalHeight]);

  // 查找当前时间对应的活跃片段索引
  const findActiveIndex = useCallback(
    (time: number): number => {
      if (!orderedTranscript.length) return -1;

      // 使用二分查找提高性能
      let low = 0;
      let high = orderedTranscript.length - 1;

      while (low <= high) {
        const mid = Math.floor((low + high) / 2);
        const seg = orderedTranscript[mid];

        if (time >= seg.startTime && time < seg.endTime) {
          return mid;
        } else if (time < seg.startTime) {
          high = mid - 1;
        } else {
          low = mid + 1;
        }
      }

      // 如果没找到精确匹配，返回最接近的索引
      return Math.max(0, Math.min(orderedTranscript.length - 1, low));
    },
    [orderedTranscript],
  );

  // 滚动到活跃片段
  const scrollToActive = useCallback(
    (index: number, force: boolean = false) => {
      if (index < 0 || index >= orderedTranscript.length) return;
      if (!force && index === lastActiveIndexRef.current) return;

      const container = listContainerRef.current;
      if (!container) return;

      // 清理之前的滚动定时器
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      scrollTimeoutRef.current = setTimeout(
        () => {
          const itemTop = offsets[index] || 0;
          const itemHeight =
            itemHeightsRef.current.get(String(orderedTranscript[index].id)) ||
            ESTIMATED_ITEM_HEIGHT;
          const containerHeight = container.clientHeight;
          const targetScrollTop = Math.max(0, itemTop - containerHeight / 3);

          container.scrollTo({ top: targetScrollTop, behavior: force ? 'auto' : 'smooth' });
          lastScrollTimeRef.current = Date.now();
          lastActiveIndexRef.current = index;
        },
        force ? 0 : 100,
      );
    },
    [orderedTranscript, offsets],
  );

  // 自动更新活跃索引
  useEffect(() => {
    if (manualClickRef.current) return;

    const nextIndex = findActiveIndex(state.playedSeconds);
    if (nextIndex !== activeIndex) {
      setActiveIndex(nextIndex);
      const now = Date.now();
      if (now - lastScrollTimeRef.current >= 500) {
        scrollToActive(nextIndex);
      }
    }
  }, [state.playedSeconds, activeIndex, findActiveIndex, scrollToActive]);

  // 安全跳转播放时间
  const safeSeek = useCallback((seconds: number) => {
    const player = playerRef.current;
    if (player?.seekTo) {
      player.seekTo(seconds, 'seconds');
      return;
    }
    pendingSeekRef.current = seconds;
  }, []);

  // 进度条控制
  const handleSeekMouseDown = () => {
    setState((prev) => ({ ...prev, seeking: true }));
  };

  const handleSeekChange = (event: React.SyntheticEvent<HTMLInputElement>) => {
    const played = Number.parseFloat((event.target as HTMLInputElement).value);
    setState((prev) => ({ ...prev, played }));
  };

  const handleSeekMouseUp = (event: React.SyntheticEvent<HTMLInputElement>) => {
    const played = Number.parseFloat((event.target as HTMLInputElement).value);
    setState((prev) => ({ ...prev, seeking: false }));

    if (playerRef.current) {
      const effectiveDuration = getEffectiveDuration();
      if (!effectiveDuration) return;

      const second = Math.min(played * effectiveDuration, effectiveDuration);
      playerRef.current.currentTime = second;
      setState((prev) => ({ ...prev, played, playedSeconds: second }));

      const idx = findActiveIndex(second);
      if (idx >= 0) {
        scrollToActive(idx, true);
        setActiveIndex(idx);
        lastScrollTimeRef.current = Date.now();
      }
    }
  };

  const handleReady = () => {
    setState((prev) => ({ ...prev, isReady: true }));
  };

  // 播放状态控制
  const handlePlay = useCallback(() => {
    setState((prev) => ({ ...prev, playing: true }));
  }, []);

  const handlePause = useCallback(() => {
    setState((prev) => ({ ...prev, playing: false }));
  }, []);

  const handleEnded = useCallback(() => {
    setState((prev) => ({ ...prev, playing: false }));
  }, []);

  // 播放器事件处理
  const handleDurationChange = () => {
    const player = playerRef.current;
    if (player && player.duration && isFinite(player.duration) && player.duration > 0) {
      setState((prev) => ({ ...prev, duration: player.duration }));
    }
  };

  const handleProgress = () => {
    const player = playerRef.current;
    if (!player || state.seeking || !player.buffered?.length) return;
    const effectiveDuration = getEffectiveDuration();
    if (!effectiveDuration) return;

    setState((prev) => ({
      ...prev,
      loadedSeconds: player.buffered.end(player.buffered.length - 1),
      loaded: player.buffered.end(player.buffered.length - 1) / effectiveDuration,
    }));
  };

  const handleRateChange = () => {
    const player = playerRef.current;
    if (player) {
      setState((prev) => ({ ...prev, playbackRate: player.playbackRate }));
    }
  };

  const handleTimeUpdate = () => {
    const player = playerRef.current;
    if (!player || state.seeking) return;

    const effectiveDuration = getEffectiveDuration();
    if (!effectiveDuration) return;

    const currentTime = player.currentTime || 0;
    const playedRatio = Math.min(currentTime / effectiveDuration, 1);

    setState((prev) => ({
      ...prev,
      playedSeconds: currentTime,
      played: playedRatio,
    }));
  };

  // 音量和播放速率控制
  const handleVolumeChange = useCallback((event: React.SyntheticEvent<HTMLInputElement>) => {
    const volume = Number.parseFloat((event.target as HTMLInputElement).value);
    setState((prev) => ({ ...prev, volume }));
  }, []);

  const handleToggleMuted = useCallback(() => {
    setState((prev) => ({ ...prev, muted: !prev.muted }));
  }, []);

  const handleSetPlaybackRate = (rate: any) => {
    setIsSelectRate(true);
    setState((prev) => ({ ...prev, playbackRate: rate }));
  };

  // 点击片段跳转播放
  const handleItemClick = (index: number) => {
    const seg = orderedTranscript[index];
    if (!seg) return;

    manualClickRef.current = true;
    if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

    safeSeek(seg.startTime);
    playerRef.current.currentTime = seg.startTime;

    const effectiveDuration = getEffectiveDuration();
    if (effectiveDuration) {
      setState((prev) => ({
        ...prev,
        played: Math.min(seg.startTime / effectiveDuration, 1),
        playing: true,
      }));
    }

    scrollToActive(index, true);
    setActiveIndex(index);

    setTimeout(() => {
      manualClickRef.current = false;
    }, 500);
  };

  // 上一个/下一个对话
  const getPrevDialog = (): { time: number; index: number } | null => {
    if (orderedTranscript.length === 0 || activeIndex <= 0) return null;
    const prevIndex = activeIndex - 1;
    const prevSeg = orderedTranscript[prevIndex];
    return { time: prevSeg.startTime, index: prevIndex };
  };

  const getNextDialog = (): { time: number; index: number } | null => {
    if (orderedTranscript.length === 0) return null;
    const lastIndex = orderedTranscript.length - 1;
    if (activeIndex === -1) return { time: orderedTranscript[0].startTime, index: 0 };
    if (activeIndex >= lastIndex) return null;
    const nextIndex = activeIndex + 1;
    const nextSeg = orderedTranscript[nextIndex];
    return { time: nextSeg.startTime, index: nextIndex };
  };

  // 上一个对话处理
  const handlePrevDialog = () => {
    const result = getPrevDialog();
    if (result) {
      const { time, index } = result;
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      playerRef.current.currentTime = time;
      const effectiveDuration = getEffectiveDuration();
      if (effectiveDuration) {
        setState((prev) => ({
          ...prev,
          played: Math.min(time / effectiveDuration, 1),
          playing: true,
        }));
      }
      scrollToActive(index, true);
      setActiveIndex(index);
    }
  };

  // 下一个对话处理
  const handleNextDialog = () => {
    const result = getNextDialog();
    if (result) {
      const { time, index } = result;
      if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);

      playerRef.current.currentTime = time;
      const effectiveDuration = getEffectiveDuration();
      if (effectiveDuration) {
        setState((prev) => ({
          ...prev,
          played: Math.min(time / effectiveDuration, 1),
          playing: true,
        }));
      }
      scrollToActive(index, true);
      setActiveIndex(index);
    }
  };

  // 编辑相关功能
  const startEdit = async (segId: string | number, initial: string) => {
    setEditingId(segId);
    setEditingText(initial);
    setState((prev) => ({ ...prev, playing: false }));
    playerRef.current.pause();
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditingText('');
  };

  const handleEditKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      saveEdit();
    }
  };

  const saveEdit = async () => {
    try {
      const p = {
        id: editingId,
        speechContent: editingText,
      };
      const res = await updateSpeechContent(p);
      if (res.data) {
        setTranscriptData((prev) =>
          prev.map((s) => (s.id == editingId ? { ...s, speechContent: editingText } : s)),
        );
        setEditingId(null);
        setEditingText('');
        Toast.success('修改成功！');
      }
    } catch (e) {
      Toast.error('修改失败！');
    }
  };

  // 滚动控制
  const scrollToTop = () => {
    setState((prev) => ({ ...prev, playing: false }));
    playerRef.current.pause();
    const container = listContainerRef.current;
    if (!container) return;
    container.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 修复scrollToBottom函数，使用递归检测确保完全触底
  const scrollToBottom = () => {
    setState((prev) => ({ ...prev, playing: false }));
    playerRef.current.pause();
    const container = listContainerRef.current;
    if (!container) return;

    // 使用更可靠的方法滚动到底部
    const scrollToBottomRecursive = (attempts = 0) => {
      if (attempts > 10) return; // 最多尝试10次

      const scrollHeight = container.scrollHeight;
      const clientHeight = container.clientHeight;
      const scrollTop = container.scrollTop;

      // 如果已经到底部，停止递归
      if (scrollTop + clientHeight >= scrollHeight - 2) return;

      // 滚动到底部
      container.scrollTo({ top: scrollHeight, behavior: 'auto' });

      // 等待一段时间后再次检查
      setTimeout(() => scrollToBottomRecursive(attempts + 1), 200);
    };

    scrollToBottomRecursive();
  };

  const RateSelectRender = (props: any) => {
    const { value } = props;
    const displayValue = Array.isArray(value)
      ? value.map((item: any) => (typeof item === 'object' ? item.label : item)).join(' / ')
      : value;

    return (
      <div className={styles.rateSelect}>
        <Tag size="large" color="white" shape="circle">
          {displayValue}
        </Tag>
      </div>
    );
  };

  return (
    <div className={styles.meetingSummaryWrapper}>
      <Spin spinning={loading} tip="加载中...">
        {/* 对话列表 */}
        <div className={styles.transcriptContainer} ref={listContainerRef}>
          {/* 顶部占位 */}
          <div style={{ height: topSpacerHeight }} />

          {/* 渲染可视窗口内的项 */}
          {orderedTranscript.slice(startIndex, endIndex + 1).map((seg, localIdx) => {
            const index = startIndex + localIdx;
            const isActive = index === activeIndex;
            const isEditing = seg.id === editingId;

            return (
              <div
                key={seg.id}
                ref={(el) => {
                  if (el) {
                    segmentRefs.current.set(String(seg.id), el);
                    const measured = el.offsetHeight;
                    const id = String(seg.id);
                    const prev = itemHeightsRef.current.get(id);

                    if (measured && measured !== prev) {
                      itemHeightsRef.current.set(id, measured);
                      setHeightsVersion((v) => v + 1);
                    }
                  }
                }}
                className={`${styles.transcriptItem} ${
                  isActive ? styles.transcriptItemActive : ''
                }`}
                onClick={() => !isEditing && handleItemClick(index)}
              >
                <div className={styles.transcriptMeta}>
                  <MeetingAvatarIcon />
                  {seg.personName && (
                    <span className={styles.transcriptSpeaker}>{seg.personName}</span>
                  )}
                  <span className={styles.transcriptTime}>{seg.startTimeStr}</span>
                  {!isEditing ? (
                    <Button
                      className={styles.editButton}
                      icon={<EditIcon />}
                      theme="borderless"
                      onClick={(e) => {
                        e.stopPropagation();
                        startEdit(seg.id, seg.speechContent);
                      }}
                    />
                  ) : (
                    <div className={styles.saveAction}>
                      <Button
                        className={styles.editButton}
                        theme="borderless"
                        type="primary"
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          saveEdit();
                        }}
                      >
                        保存
                      </Button>
                      <Button
                        className={styles.editButton}
                        theme="borderless"
                        type="tertiary"
                        onClick={(e) => {
                          e.stopPropagation();
                          cancelEdit();
                        }}
                      >
                        取消
                      </Button>
                    </div>
                  )}
                </div>
                {!isEditing && seg.speechContent ? (
                  <Paragraph
                    className={styles.transcriptText}
                    ellipsis={{
                      rows: 5,
                      expandable: true,
                      collapsible: true,
                      expandText: '展开全部',
                      collapseText: '收起',
                      onExpand: (b, e) => e.stopPropagation(),
                    }}
                  >
                    {seg.speechContent}
                  </Paragraph>
                ) : (
                  <TextArea
                    className={styles.editTextarea}
                    autosize
                    value={editingText}
                    onChange={setEditingText}
                    onKeyDown={handleEditKeyDown}
                    rows={3}
                    onClick={(e) => e.stopPropagation()}
                  />
                )}
              </div>
            );
          })}

          {/* 底部占位 */}
          <div style={{ height: bottomSpacerHeight }} />
        </div>

        {/* 音频控制栏 */}
        <div className={styles.audioBar}>
          <div className={styles.audioControls}>
            <div className={styles.controlGroup}>
              <Button
                icon={activeIndex <= 0 ? <AudioForwardDisabledIcon /> : <AudioForwardIcon />}
                aria-label="上一个对话"
                theme="borderless"
                disabled={activeIndex <= 0}
                onClick={handlePrevDialog}
              />
              <Button
                icon={state.playing ? <AudioPauseIcon /> : <AudioPlayIcon />}
                aria-label={state.playing ? '暂停' : '播放'}
                theme="borderless"
                className={styles.playButton}
                onClick={() => {
                  setState((prev) => ({ ...prev, playing: !prev.playing }));
                }}
              />
              <Button
                icon={
                  activeIndex >= orderedTranscript.length - 1 ? (
                    <AudioBackwardDisabledIcon />
                  ) : (
                    <AudioBackwardIcon />
                  )
                }
                aria-label="下一个对话"
                theme="borderless"
                onClick={handleNextDialog}
                disabled={activeIndex >= orderedTranscript.length - 1}
              />
            </div>
            <input
              type="range"
              min={0}
              max={1}
              step="any"
              value={state.played}
              style={{
                background: `linear-gradient(
                to right,
                #005BF8 ${state.played * 100}%,
                #ebeef2 ${state.played * 100}%
              )`,
              }}
              onMouseDown={handleSeekMouseDown}
              onChange={handleSeekChange}
              onMouseUp={handleSeekMouseUp}
              className={styles.progressSlider}
            />
            <div className={styles.timeInfo}>
              <span>{secondsToTimestamp(state.playedSeconds)}</span>
              <span> / </span>
              <span>{secondsToTimestamp(getEffectiveDuration())}</span>
            </div>

            <Tooltip content={state.muted ? '已静音' : '已开启'}>
              <Button className={styles.audioButton} theme="borderless" onClick={handleToggleMuted}>
                {state.muted ? <AudioNotMutedIcon /> : <AudioMutedIcon />}
              </Button>
            </Tooltip>

            <Select
              className={styles.playbackRateSelect}
              value={state.playbackRate}
              onChange={handleSetPlaybackRate}
              triggerRender={RateSelectRender}
              optionList={PLAYBACK_RATE_OPTIONS}
            ></Select>
          </div>

          {/* 音频播放器 */}
          <ReactPlayer
            ref={playerRef}
            src={audioSrc}
            playing={state.playing}
            muted={state.muted}
            volume={state.volume}
            playbackRate={state.playbackRate}
            height={0}
            width={0}
            preload="metadata"
            onReady={handleReady}
            onPlay={handlePlay}
            onPause={handlePause}
            onEnded={handleEnded}
            onDurationChange={handleDurationChange}
            onProgress={handleProgress}
            onTimeUpdate={handleTimeUpdate}
            onRateChange={handleRateChange}
            config={
              {
                file: { forceAudio: true, attributes: { preload: 'metadata' } },
              } as any
            }
          />
        </div>
      </Spin>

      {/* 滚动控制按钮 */}
      <div
        className={classNames(styles.scrollControls, {
          [styles.scrollControlsWrap]: pageMode === 'meeting',
        })}
      >
        <BackTopIcon className={styles.scrollIcon} onClick={scrollToTop} />
        <BackTopIcon
          className={`${styles.scrollIcon} ${styles.scrollIconBottom}`}
          onClick={scrollToBottom}
        />
      </div>
    </div>
  );
};

export default AudioTranscriptPlayer;
